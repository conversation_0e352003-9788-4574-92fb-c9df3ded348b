import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

export const generateUploadUrl = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");
    
    return await ctx.storage.generateUploadUrl();
  },
});

export const uploadVideo = mutation({
  args: {
    projectId: v.id("projects"),
    name: v.string(),
    description: v.optional(v.string()),
    fileId: v.id("_storage"),
    duration: v.number(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");
    
    // Verify user has access to project
    const project = await ctx.db.get(args.projectId);
    if (!project) throw new Error("Project not found");
    
    const isOwner = project.ownerId === userId;
    const membership = await ctx.db
      .query("projectMembers")
      .withIndex("by_project_user", (q) => 
        q.eq("projectId", args.projectId).eq("userId", userId)
      )
      .unique();
    
    if (!isOwner && (!membership || !["admin", "editor"].includes(membership.role))) {
      throw new Error("Not authorized to upload videos");
    }
    
    // Mark previous videos as not latest
    const existingVideos = await ctx.db
      .query("videos")
      .withIndex("by_project_latest", (q) => 
        q.eq("projectId", args.projectId).eq("isLatest", true)
      )
      .collect();
    
    for (const video of existingVideos) {
      await ctx.db.patch(video._id, { isLatest: false });
    }
    
    // Get next version number
    const allVideos = await ctx.db
      .query("videos")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .collect();
    
    const nextVersion = Math.max(0, ...allVideos.map(v => v.version)) + 1;
    
    // Insert new video
    const videoId = await ctx.db.insert("videos", {
      projectId: args.projectId,
      name: args.name,
      description: args.description,
      fileId: args.fileId,
      duration: args.duration,
      version: nextVersion,
      isLatest: true,
      uploadedBy: userId,
      uploadedAt: Date.now(),
      status: "ready",
    });
    
    // Update project timestamp
    await ctx.db.patch(args.projectId, {
      updatedAt: Date.now(),
    });
    
    return videoId;
  },
});

export const getVideoUrl = query({
  args: { videoId: v.id("videos") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return null;
    
    const video = await ctx.db.get(args.videoId);
    if (!video) return null;
    
    // Check access to project
    const project = await ctx.db.get(video.projectId);
    if (!project) return null;
    
    const isOwner = project.ownerId === userId;
    const membership = await ctx.db
      .query("projectMembers")
      .withIndex("by_project_user", (q) => 
        q.eq("projectId", video.projectId).eq("userId", userId)
      )
      .unique();
    
    if (!isOwner && !membership) return null;
    
    const url = await ctx.storage.getUrl(video.fileId);
    return { ...video, url };
  },
});

export const getProjectVideos = query({
  args: { projectId: v.id("projects") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return [];
    
    // Check access to project
    const project = await ctx.db.get(args.projectId);
    if (!project) return [];
    
    const isOwner = project.ownerId === userId;
    const membership = await ctx.db
      .query("projectMembers")
      .withIndex("by_project_user", (q) => 
        q.eq("projectId", args.projectId).eq("userId", userId)
      )
      .unique();
    
    if (!isOwner && !membership) return [];
    
    const videos = await ctx.db
      .query("videos")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .order("desc")
      .collect();
    
    return Promise.all(
      videos.map(async (video) => {
        const url = await ctx.storage.getUrl(video.fileId);
        const uploader = await ctx.db.get(video.uploadedBy);
        const uploaderProfile = await ctx.db
          .query("userProfiles")
          .withIndex("by_user", (q) => q.eq("userId", video.uploadedBy))
          .unique();
        
        return {
          ...video,
          url,
          uploader,
          uploaderProfile,
        };
      })
    );
  },
});
