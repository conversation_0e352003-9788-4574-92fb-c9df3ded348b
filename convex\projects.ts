import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

export const listUserProjects = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return [];
    
    // Get projects where user is owner or member
    const ownedProjects = await ctx.db
      .query("projects")
      .withIndex("by_owner", (q) => q.eq("ownerId", userId))
      .collect();
    
    const memberProjects = await ctx.db
      .query("projectMembers")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();
    
    const memberProjectIds = memberProjects.map(m => m.projectId);
    const memberProjectsData = await Promise.all(
      memberProjectIds.map(id => ctx.db.get(id))
    );
    
    const allProjects = [
      ...ownedProjects,
      ...memberProjectsData.filter(p => p !== null)
    ];
    
    // Remove duplicates and add member info
    const uniqueProjects = Array.from(
      new Map(allProjects.map(p => [p!._id, p])).values()
    );
    
    return Promise.all(
      uniqueProjects.map(async (project) => {
        const memberCount = await ctx.db
          .query("projectMembers")
          .withIndex("by_project", (q) => q.eq("projectId", project!._id))
          .collect();
        
        const latestVideo = await ctx.db
          .query("videos")
          .withIndex("by_project_latest", (q) => 
            q.eq("projectId", project!._id).eq("isLatest", true)
          )
          .first();
        
        return {
          ...project,
          memberCount: memberCount.length + 1, // +1 for owner
          hasVideo: !!latestVideo,
          lastActivity: project!.updatedAt,
        };
      })
    );
  },
});

export const createProject = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");
    
    const now = Date.now();
    
    return await ctx.db.insert("projects", {
      name: args.name,
      description: args.description,
      ownerId: userId,
      status: "active",
      createdAt: now,
      updatedAt: now,
    });
  },
});

export const getProject = query({
  args: { projectId: v.id("projects") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return null;
    
    const project = await ctx.db.get(args.projectId);
    if (!project) return null;
    
    // Check if user has access
    const isOwner = project.ownerId === userId;
    const membership = await ctx.db
      .query("projectMembers")
      .withIndex("by_project_user", (q) => 
        q.eq("projectId", args.projectId).eq("userId", userId)
      )
      .unique();
    
    if (!isOwner && !membership) return null;
    
    // Get project members
    const members = await ctx.db
      .query("projectMembers")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .collect();
    
    const memberDetails = await Promise.all(
      members.map(async (member) => {
        const user = await ctx.db.get(member.userId);
        const profile = await ctx.db
          .query("userProfiles")
          .withIndex("by_user", (q) => q.eq("userId", member.userId))
          .unique();
        
        return {
          ...member,
          user,
          profile,
        };
      })
    );
    
    // Get latest video
    const latestVideo = await ctx.db
      .query("videos")
      .withIndex("by_project_latest", (q) => 
        q.eq("projectId", args.projectId).eq("isLatest", true)
      )
      .first();
    
    return {
      ...project,
      members: memberDetails,
      latestVideo,
      userRole: isOwner ? "admin" : membership?.role,
    };
  },
});

export const inviteToProject = mutation({
  args: {
    projectId: v.id("projects"),
    email: v.string(),
    role: v.union(v.literal("admin"), v.literal("editor"), v.literal("translator"), v.literal("reviewer"), v.literal("viewer")),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");
    
    const project = await ctx.db.get(args.projectId);
    if (!project) throw new Error("Project not found");
    
    // Check if user can invite (owner or admin)
    const isOwner = project.ownerId === userId;
    const membership = await ctx.db
      .query("projectMembers")
      .withIndex("by_project_user", (q) => 
        q.eq("projectId", args.projectId).eq("userId", userId)
      )
      .unique();
    
    if (!isOwner && (!membership || membership.role !== "admin")) {
      throw new Error("Not authorized to invite");
    }
    
    // Generate invite token
    const token = Math.random().toString(36).substring(2, 15) + 
                  Math.random().toString(36).substring(2, 15);
    
    const expiresAt = Date.now() + (7 * 24 * 60 * 60 * 1000); // 7 days
    
    return await ctx.db.insert("invites", {
      projectId: args.projectId,
      email: args.email,
      role: args.role,
      invitedBy: userId,
      token,
      status: "pending",
      expiresAt,
      createdAt: Date.now(),
    });
  },
});
