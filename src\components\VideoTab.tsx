import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Upload, Play, Clock, User, MoreVertical } from "lucide-react";
import { VideoUpload } from "./VideoUpload";
import { VideoPlayer } from "./VideoPlayer";
import { Id } from "../../convex/_generated/dataModel";

interface VideoTabProps {
  projectId: string;
}

export function VideoTab({ projectId }: VideoTabProps) {
  const [showUpload, setShowUpload] = useState(false);
  const [selectedVideoId, setSelectedVideoId] = useState<string | null>(null);
  
  const videos = useQuery(api.videos.getProjectVideos, { 
    projectId: projectId as Id<"projects"> 
  }) || [];

  const latestVideo = videos.find(v => v.isLatest);
  const currentVideo = selectedVideoId 
    ? videos.find(v => v._id === selectedVideoId) 
    : latestVideo;

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-6">
      {/* Upload Button */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">Project Videos</h3>
        <button
          onClick={() => setShowUpload(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Upload className="w-4 h-4" />
          <span>Upload Video</span>
        </button>
      </div>

      {videos.length === 0 ? (
        /* Empty State */
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
          <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No videos uploaded yet</h3>
          <p className="text-gray-600 mb-6">Upload your first video to start collaborating with your team</p>
          <button
            onClick={() => setShowUpload(true)}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Upload Your First Video
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Video Player */}
          <div className="lg:col-span-2 space-y-4">
            {currentVideo ? (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900">{currentVideo.name}</h4>
                    {currentVideo.description && (
                      <p className="text-gray-600 text-sm mt-1">{currentVideo.description}</p>
                    )}
                  </div>
                  {currentVideo.isLatest && (
                    <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                      Latest
                    </span>
                  )}
                </div>
                
                <VideoPlayer
                  videoId={currentVideo._id}
                  onTimeUpdate={(time) => {
                    // Handle time updates for comments
                  }}
                  onCommentClick={(timestamp) => {
                    // Handle comment clicks
                  }}
                />
                
                <div className="flex items-center justify-between mt-4 text-sm text-gray-500">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>{formatDuration(currentVideo.duration)}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <User className="w-4 h-4" />
                      <span>
                        {currentVideo.uploaderProfile?.displayName || 
                         currentVideo.uploader?.email || 
                         "Unknown"}
                      </span>
                    </div>
                  </div>
                  <span>v{currentVideo.version}</span>
                </div>
              </div>
            ) : (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="bg-gray-100 rounded-lg aspect-video flex items-center justify-center">
                  <Play className="w-12 h-12 text-gray-400" />
                </div>
              </div>
            )}
          </div>

          {/* Video List */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h4 className="font-semibold text-gray-900 mb-4">All Versions</h4>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {videos.map((video) => (
                <div
                  key={video._id}
                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                    selectedVideoId === video._id || (!selectedVideoId && video.isLatest)
                      ? "border-blue-500 bg-blue-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                  onClick={() => setSelectedVideoId(video._id)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h5 className="font-medium text-gray-900 text-sm truncate">
                          {video.name}
                        </h5>
                        {video.isLatest && (
                          <span className="px-1.5 py-0.5 bg-green-100 text-green-700 text-xs font-medium rounded">
                            Latest
                          </span>
                        )}
                      </div>
                      <div className="text-xs text-gray-500 space-y-1">
                        <div>v{video.version} • {formatDuration(video.duration)}</div>
                        <div>{formatDate(video.uploadedAt)}</div>
                        <div>
                          by {video.uploaderProfile?.displayName || 
                              video.uploader?.email || 
                              "Unknown"}
                        </div>
                      </div>
                    </div>
                    <button className="p-1 text-gray-400 hover:text-gray-600">
                      <MoreVertical className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Upload Modal */}
      {showUpload && (
        <VideoUpload
          projectId={projectId}
          onClose={() => setShowUpload(false)}
          onSuccess={() => {
            setShowUpload(false);
            // The query will automatically refresh
          }}
        />
      )}
    </div>
  );
}
