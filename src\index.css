@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --color-light: #ffffff;
  --color-dark: #171717;
}

.accent-text {
  @apply text-slate-600;
}

body {
  font-family:
    "Inter Variable",
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Robot<PERSON>,
    "Helvetica Neue",
    <PERSON><PERSON>,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
  color: var(--color-dark);
  background: var(--color-light);
}

/* Auth input fields */
.auth-input-field {
  @apply w-full px-4 py-3 rounded-lg bg-white border border-gray-200 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-shadow shadow-sm hover:shadow;
}

/* Auth buttons */
.auth-button {
  @apply w-full px-4 py-3 rounded-lg bg-blue-600 text-white font-semibold hover:bg-blue-700 transition-colors shadow-sm hover:shadow disabled:opacity-50 disabled:cursor-not-allowed;
}

/* Custom utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Smooth transitions */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

/* Button variants */
.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

/* Card styles */
.card {
  @apply bg-white rounded-xl shadow-sm border border-gray-200;
}

.card-hover {
  @apply hover:shadow-md transition-shadow;
}

/* Status indicators */
.status-active {
  @apply bg-green-100 text-green-800;
}

.status-completed {
  @apply bg-blue-100 text-blue-800;
}

.status-archived {
  @apply bg-gray-100 text-gray-800;
}

/* Video Player Styles */
.slider {
  background: linear-gradient(to right, #3b82f6 0%, #3b82f6 var(--value, 0%), rgba(255,255,255,0.3) var(--value, 0%), rgba(255,255,255,0.3) 100%);
}

.slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Video container aspect ratio */
.aspect-video {
  aspect-ratio: 16 / 9;
}

/* Mobile optimizations */
@media (max-width: 640px) {
  .mobile-padding {
    @apply px-4;
  }
  
  .mobile-text {
    @apply text-sm;
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  .dark-mode {
    --color-light: #0f172a;
    --color-dark: #f8fafc;
  }
}
