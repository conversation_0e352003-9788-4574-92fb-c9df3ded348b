import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { User, Video, Globe, Users, CheckCircle } from "lucide-react";

interface OnboardingFlowProps {
  onComplete: () => void;
}

export function OnboardingFlow({ onComplete }: OnboardingFlowProps) {
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    displayName: "",
    role: "" as "creator" | "editor" | "translator" | "reviewer" | "client",
  });

  const createProfile = useMutation(api.users.createUserProfile);
  const completeOnboarding = useMutation(api.users.completeOnboarding);

  const roles = [
    {
      id: "creator" as const,
      title: "Creator",
      description: "YouTube creator, content producer, or channel owner",
      icon: Video,
      color: "bg-blue-100 text-blue-600",
    },
    {
      id: "editor" as const,
      title: "Video Editor",
      description: "Video editor, motion graphics artist, or post-production specialist",
      icon: User,
      color: "bg-green-100 text-green-600",
    },
    {
      id: "translator" as const,
      title: "Translator",
      description: "Subtitle translator, localization specialist, or language expert",
      icon: Globe,
      color: "bg-purple-100 text-purple-600",
    },
    {
      id: "reviewer" as const,
      title: "Reviewer",
      description: "Content reviewer, quality assurance, or project manager",
      icon: CheckCircle,
      color: "bg-orange-100 text-orange-600",
    },
    {
      id: "client" as const,
      title: "Client",
      description: "Brand representative, sponsor, or external stakeholder",
      icon: Users,
      color: "bg-red-100 text-red-600",
    },
  ];

  const handleNext = async () => {
    if (step === 1) {
      setStep(2);
    } else if (step === 2) {
      try {
        await createProfile({
          displayName: formData.displayName,
          role: formData.role,
        });
        await completeOnboarding();
        onComplete();
      } catch (error) {
        console.error("Failed to complete onboarding:", error);
      }
    }
  };

  const canProceed = () => {
    if (step === 1) return formData.displayName.trim().length > 0;
    if (step === 2) return formData.role.length > 0;
    return false;
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full">
        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex items-center justify-center space-x-4 mb-4">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
              step >= 1 ? "bg-blue-600 text-white" : "bg-gray-200 text-gray-600"
            }`}>
              1
            </div>
            <div className={`w-16 h-1 ${step >= 2 ? "bg-blue-600" : "bg-gray-200"}`}></div>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
              step >= 2 ? "bg-blue-600 text-white" : "bg-gray-200 text-gray-600"
            }`}>
              2
            </div>
          </div>
          <p className="text-center text-gray-600">
            Step {step} of 2: {step === 1 ? "Personal Info" : "Choose Your Role"}
          </p>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
          {step === 1 && (
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Welcome to Flimio!</h2>
              <p className="text-gray-600 mb-8">Let's get you set up with a profile</p>
              
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Display Name
                  </label>
                  <input
                    type="text"
                    value={formData.displayName}
                    onChange={(e) => setFormData({ ...formData, displayName: e.target.value })}
                    placeholder="Enter your display name"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    This is how other team members will see you
                  </p>
                </div>
              </div>
            </div>
          )}

          {step === 2 && (
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">What's your role?</h2>
              <p className="text-gray-600 mb-8">
                This helps us customize your experience and show relevant features
              </p>
              
              <div className="grid gap-4">
                {roles.map((role) => {
                  const Icon = role.icon;
                  return (
                    <button
                      key={role.id}
                      onClick={() => setFormData({ ...formData, role: role.id })}
                      className={`p-4 rounded-lg border-2 text-left transition-all ${
                        formData.role === role.id
                          ? "border-blue-500 bg-blue-50"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                    >
                      <div className="flex items-start space-x-4">
                        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${role.color}`}>
                          <Icon className="w-6 h-6" />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900 mb-1">{role.title}</h3>
                          <p className="text-gray-600 text-sm">{role.description}</p>
                        </div>
                        {formData.role === role.id && (
                          <CheckCircle className="w-5 h-5 text-blue-600" />
                        )}
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>
          )}

          <div className="flex justify-between mt-8">
            <button
              onClick={() => step > 1 && setStep(step - 1)}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                step > 1
                  ? "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                  : "text-gray-400 cursor-not-allowed"
              }`}
              disabled={step === 1}
            >
              Back
            </button>
            
            <button
              onClick={handleNext}
              disabled={!canProceed()}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                canProceed()
                  ? "bg-blue-600 text-white hover:bg-blue-700"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
              }`}
            >
              {step === 2 ? "Complete Setup" : "Next"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
