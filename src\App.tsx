import { Authenticated, Unauthenticated, useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { SignInForm } from "./SignInForm";
import { SignOutButton } from "./SignOutButton";
import { Toaster } from "sonner";
import { useState, useEffect } from "react";
import { LandingPage } from "./components/LandingPage";
import { Dashboard } from "./components/Dashboard";
import { OnboardingFlow } from "./components/OnboardingFlow";
import { ProjectView } from "./components/ProjectView";
import { Navbar } from "./components/Navbar";

export default function App() {
  const [currentPage, setCurrentPage] = useState<string>("landing");
  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(null);
  const user = useQuery(api.users.getCurrentUser);

  useEffect(() => {
    if (user && !user.profile?.onboardingCompleted) {
      setCurrentPage("onboarding");
    } else if (user && user.profile?.onboardingCompleted) {
      setCurrentPage("dashboard");
    }
  }, [user]);

  const renderContent = () => {
    if (currentPage === "landing") {
      return <LandingPage onGetStarted={() => setCurrentPage("auth")} />;
    }
    
    if (currentPage === "auth") {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md w-full mx-auto p-6">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Welcome to Flimio</h1>
              <p className="text-gray-600">Sign in to start collaborating</p>
            </div>
            <SignInForm />
          </div>
        </div>
      );
    }

    return (
      <Authenticated>
        <div className="min-h-screen bg-gray-50">
          <Navbar 
            onNavigate={setCurrentPage} 
            currentPage={currentPage}
            onProjectSelect={setSelectedProjectId}
          />
          
          <main className="pt-16">
            {currentPage === "onboarding" && (
              <OnboardingFlow onComplete={() => setCurrentPage("dashboard")} />
            )}
            
            {currentPage === "dashboard" && (
              <Dashboard 
                onProjectSelect={(projectId) => {
                  setSelectedProjectId(projectId);
                  setCurrentPage("project");
                }}
              />
            )}
            
            {currentPage === "project" && selectedProjectId && (
              <ProjectView 
                projectId={selectedProjectId}
                onBack={() => setCurrentPage("dashboard")}
              />
            )}
          </main>
        </div>
      </Authenticated>
    );
  };

  return (
    <div className="min-h-screen">
      <Unauthenticated>
        {renderContent()}
      </Unauthenticated>
      
      <Authenticated>
        {renderContent()}
      </Authenticated>
      
      <Toaster position="top-right" />
    </div>
  );
}
