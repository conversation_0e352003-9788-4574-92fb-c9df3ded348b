import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

export const getVideoComments = query({
  args: { videoId: v.id("videos") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return [];
    
    const comments = await ctx.db
      .query("comments")
      .withIndex("by_video", (q) => q.eq("videoId", args.videoId))
      .collect();
    
    return Promise.all(
      comments.map(async (comment) => {
        const author = await ctx.db.get(comment.authorId);
        const authorProfile = await ctx.db
          .query("userProfiles")
          .withIndex("by_user", (q) => q.eq("userId", comment.authorId))
          .unique();
        
        // Get replies
        const replies = await ctx.db
          .query("comments")
          .filter((q) => q.eq(q.field("parentId"), comment._id))
          .collect();
        
        const repliesWithAuthors = await Promise.all(
          replies.map(async (reply) => {
            const replyAuthor = await ctx.db.get(reply.authorId);
            const replyAuthorProfile = await ctx.db
              .query("userProfiles")
              .withIndex("by_user", (q) => q.eq("userId", reply.authorId))
              .unique();
            
            return {
              ...reply,
              author: replyAuthor,
              authorProfile: replyAuthorProfile,
            };
          })
        );
        
        return {
          ...comment,
          author,
          authorProfile,
          replies: repliesWithAuthors,
        };
      })
    );
  },
});

export const addComment = mutation({
  args: {
    videoId: v.id("videos"),
    projectId: v.id("projects"),
    content: v.string(),
    timestamp: v.number(),
    mentions: v.array(v.id("users")),
    parentId: v.optional(v.id("comments")),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");
    
    const now = Date.now();
    
    const commentId = await ctx.db.insert("comments", {
      videoId: args.videoId,
      projectId: args.projectId,
      authorId: userId,
      content: args.content,
      timestamp: args.timestamp,
      parentId: args.parentId,
      mentions: args.mentions,
      status: "open",
      priority: "medium",
      createdAt: now,
      updatedAt: now,
    });
    
    // Create notifications for mentions
    for (const mentionedUserId of args.mentions) {
      await ctx.db.insert("notifications", {
        userId: mentionedUserId,
        type: "mention",
        title: "You were mentioned in a comment",
        message: `${args.content.substring(0, 100)}...`,
        relatedId: commentId,
        read: false,
        createdAt: now,
      });
    }
    
    return commentId;
  },
});

export const resolveComment = mutation({
  args: {
    commentId: v.id("comments"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");
    
    await ctx.db.patch(args.commentId, {
      status: "resolved",
      updatedAt: Date.now(),
    });
  },
});
