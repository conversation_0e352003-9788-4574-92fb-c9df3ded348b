import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

const applicationTables = {
  // User profiles and subscription info
  userProfiles: defineTable({
    userId: v.id("users"),
    displayName: v.optional(v.string()),
    avatar: v.optional(v.id("_storage")),
    role: v.union(v.literal("creator"), v.literal("editor"), v.literal("translator"), v.literal("reviewer"), v.literal("client")),
    subscription: v.union(v.literal("free"), v.literal("pro"), v.literal("team")),
    subscriptionExpiry: v.optional(v.number()),
    onboardingCompleted: v.boolean(),
  }).index("by_user", ["userId"]),

  // Projects/workspaces
  projects: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
    ownerId: v.id("users"),
    thumbnail: v.optional(v.id("_storage")),
    status: v.union(v.literal("active"), v.literal("completed"), v.literal("archived")),
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_owner", ["ownerId"])
    .index("by_status", ["status"]),

  // Project members and their roles
  projectMembers: defineTable({
    projectId: v.id("projects"),
    userId: v.id("users"),
    role: v.union(v.literal("admin"), v.literal("editor"), v.literal("translator"), v.literal("reviewer"), v.literal("viewer")),
    invitedBy: v.id("users"),
    joinedAt: v.number(),
  }).index("by_project", ["projectId"])
    .index("by_user", ["userId"])
    .index("by_project_user", ["projectId", "userId"]),

  // Video files and versions
  videos: defineTable({
    projectId: v.id("projects"),
    name: v.string(),
    description: v.optional(v.string()),
    fileId: v.id("_storage"),
    duration: v.number(), // in seconds
    version: v.number(),
    isLatest: v.boolean(),
    uploadedBy: v.id("users"),
    uploadedAt: v.number(),
    status: v.union(v.literal("processing"), v.literal("ready"), v.literal("error")),
  }).index("by_project", ["projectId"])
    .index("by_project_latest", ["projectId", "isLatest"]),

  // Comments on videos with timestamps
  comments: defineTable({
    videoId: v.id("videos"),
    projectId: v.id("projects"),
    authorId: v.id("users"),
    content: v.string(),
    timestamp: v.number(), // in seconds
    parentId: v.optional(v.id("comments")), // for replies
    mentions: v.array(v.id("users")),
    status: v.union(v.literal("open"), v.literal("resolved"), v.literal("archived")),
    priority: v.union(v.literal("low"), v.literal("medium"), v.literal("high")),
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_video", ["videoId"])
    .index("by_project", ["projectId"])
    .index("by_status", ["status"]),

  // Subtitles and translations
  subtitles: defineTable({
    videoId: v.id("videos"),
    projectId: v.id("projects"),
    language: v.string(), // ISO language code
    isOriginal: v.boolean(),
    translatedFrom: v.optional(v.id("subtitles")),
    content: v.array(v.object({
      start: v.number(),
      end: v.number(),
      text: v.string(),
    })),
    createdBy: v.id("users"),
    status: v.union(v.literal("draft"), v.literal("review"), v.literal("approved")),
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_video", ["videoId"])
    .index("by_project", ["projectId"])
    .index("by_language", ["language"]),

  // Tasks assigned to comments or general project tasks
  tasks: defineTable({
    projectId: v.id("projects"),
    commentId: v.optional(v.id("comments")),
    title: v.string(),
    description: v.optional(v.string()),
    assignedTo: v.id("users"),
    assignedBy: v.id("users"),
    status: v.union(v.literal("todo"), v.literal("in_progress"), v.literal("review"), v.literal("completed")),
    priority: v.union(v.literal("low"), v.literal("medium"), v.literal("high")),
    dueDate: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_project", ["projectId"])
    .index("by_assignee", ["assignedTo"])
    .index("by_status", ["status"]),

  // Assets library (thumbnails, music, etc.)
  assets: defineTable({
    projectId: v.id("projects"),
    name: v.string(),
    type: v.union(v.literal("image"), v.literal("audio"), v.literal("video"), v.literal("document")),
    fileId: v.id("_storage"),
    tags: v.array(v.string()),
    uploadedBy: v.id("users"),
    uploadedAt: v.number(),
    size: v.number(), // in bytes
  }).index("by_project", ["projectId"])
    .index("by_type", ["type"]),

  // Notifications
  notifications: defineTable({
    userId: v.id("users"),
    type: v.union(
      v.literal("comment"),
      v.literal("mention"),
      v.literal("task_assigned"),
      v.literal("project_invite"),
      v.literal("video_uploaded")
    ),
    title: v.string(),
    message: v.string(),
    relatedId: v.optional(v.string()), // ID of related entity
    read: v.boolean(),
    createdAt: v.number(),
  }).index("by_user", ["userId"])
    .index("by_user_read", ["userId", "read"]),

  // Project invites
  invites: defineTable({
    projectId: v.id("projects"),
    email: v.string(),
    role: v.union(v.literal("admin"), v.literal("editor"), v.literal("translator"), v.literal("reviewer"), v.literal("viewer")),
    invitedBy: v.id("users"),
    token: v.string(),
    status: v.union(v.literal("pending"), v.literal("accepted"), v.literal("expired")),
    expiresAt: v.number(),
    createdAt: v.number(),
  }).index("by_project", ["projectId"])
    .index("by_email", ["email"])
    .index("by_token", ["token"]),
};

export default defineSchema({
  ...authTables,
  ...applicationTables,
});
